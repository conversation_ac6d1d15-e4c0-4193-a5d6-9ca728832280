import os
from tkinter import Tk, Button, filedialog, messagebox, Entry, Label
from PIL import Image

def convert_images_to_webp():
    # فتح نافذة اختيار الصور
    file_paths = filedialog.askopenfilenames(
        title="اختر الصور",
        filetypes=[("Image Files", "*.jpg *.jpeg *.png *.bmp *.tiff *.gif")]
    )
    if not file_paths:
        return  # المستخدم لم يحدد أي ملفات

    # الحصول على اسم الصورة والمجلد من المستخدم
    base_name = entry_name.get().strip()
    folder_name = entry_folder.get().strip()
    if not base_name:
        messagebox.showerror("خطأ", "يرجى إدخال اسم الصورة.")
        return
    if not folder_name:
        messagebox.showerror("خطأ", "يرجى إدخال اسم المجلد.")
        return

    output_folder = os.path.join(os.getcwd(), folder_name)
    os.makedirs(output_folder, exist_ok=True)
    converted = 0

    for idx, path in enumerate(file_paths, start=1):
        try:
            img = Image.open(path).convert("RGB")
            # إذا كان هناك أكثر من صورة، أضف رقم تسلسلي
            if len(file_paths) == 1:
                filename = base_name
            else:
                filename = f"{base_name}_{idx}"
            output_path = os.path.join(output_folder, f"{filename}.webp")
            img.save(output_path, "webp")
            converted += 1
        except Exception as e:
            print(f"فشل تحويل {path}: {e}")

    messagebox.showinfo("تم التحويل", f"تم تحويل {converted} صورة بنجاح إلى WebP!\nالمجلد: {output_folder}")

# واجهة المستخدم
root = Tk()
root.title("تحويل الصور إلى WebP")
root.geometry("350x220")

Label(root, text="اسم الصورة بعد التحويل:").pack(pady=(10,0))
entry_name = Entry(root, width=30)
entry_name.pack()

Label(root, text="اسم المجلد الجديد:").pack(pady=(10,0))
entry_folder = Entry(root, width=30)
entry_folder.pack()

btn_convert = Button(root, text="اختر صور لتحويلها إلى WebP", command=convert_images_to_webp)
btn_convert.pack(expand=True, pady=20)

root.mainloop()
